# 🚀 快速开始

### 🌧️ 环境要求

- **现代浏览器** - Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **本地服务器** - Python/Node.js/PHP 任一（开发时）
- **无框架依赖** - 纯原生JavaScript实现

### 🛠️ 本地运行

1. **克隆项目**

   ```bash
   git clone https://github.com/your-repo/fs-oss-navigation.git
   cd fs-oss-navigation
   ```

2. **启动本地服务器**

   ```bash
   # 使用Python (推荐)
   python -m http.server 8000

   # 使用Node.js
   npx serve . -p 8000

   # 使用PHP
   php -S localhost:8000
   ```

3. **访问应用**

   打开浏览器访问 `http://localhost:8000`

## 🌐 部署方案

#### 静态托管平台

- **GitHub Pages** - 免费，支持自定义域名
- **Vercel** - 全球CDN，自动部署
- **Netlify** - 表单处理，边缘函数
- **阿里云OSS** - 国内访问优化
- **腾讯云COS** - 企业级存储

#### 一键部署

```bash
# Vercel部署
npx vercel --prod

# Netlify部署
npx netlify-cli deploy --prod --dir .
```

### 🖥️ 服务器部署

#### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/fs-oss-navigation;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_types
        text/css
        application/javascript
        application/json
        image/svg+xml;

    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # HTML文件缓存
    location ~* \.html$ {
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
}