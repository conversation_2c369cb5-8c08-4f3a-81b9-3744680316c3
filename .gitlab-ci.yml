stages:
  - build
  - deploy

workflow:
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      when: always

variables:
  DEPLOY_APP_NAME: "fs-oss-navigation"
  DEPLOY_APP_DOCKER_IMAGE: "reg.firstshare.cn/app/fs-oss-navigation:${CI_COMMIT_REF_NAME}"

build-oss-navigation:
  stage: build
  tags:
    - docker-build
  script:
    - docker build --pull -t ${DEPLOY_APP_DOCKER_IMAGE} .
    - docker push ${DEPLOY_APP_DOCKER_IMAGE}


firstshare-k8s1:
  stage: deploy
  tags:
    - k8s-deploy
  image: reg.firstshare.cn/base/fs-kubectl:v3.0
  variables:
    DEPLOY_K8S_CLUSTER: "firstshare-k8s1"
    DEPLOY_K8S_NAMESPACE: "firstshare"
    DEPLOY_APP_REPLICAS: "2"
    DEPLOY_APP_REQUESTS_CPU: "100m"
    DEPLOY_APP_REQUESTS_MEMORY: "20Mi"
    DEPLOY_APP_LIMITS_CPU: "200m"
    DEPLOY_APP_LIMITS_MEMORY: "50Mi"
  script:
    - kubectl-proxy apply --print-manifest -f k8s-manifest/deployment.yml
    - kubectl-proxy apply --print-manifest -f k8s-manifest/service.yml

