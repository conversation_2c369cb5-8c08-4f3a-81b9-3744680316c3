{"categories": [{"id": "foneshare-cloud", "name": "多云管理", "icon": "🌐", "children": [{"id": "foneshare-cloud-other", "name": "其他", "icon": "🌧️", "children": [{"id": "foneshare-cloud-other-cloudmodel", "name": "模版云", "icon": "🌧️", "sites": [{"id": "foneshare-cloud-other-cloudmodel-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://cloudmodel-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-other-cloudmodel-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://cloudmodel-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-other-cloudmodel-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-alt", "url": "https://cloudmodel-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-other-cloudmodel-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-other-cloudmodel-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://cloudmodel-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-other-cloudmodel-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://cloudmodel-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-other-cloudmodel-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://cloudmodel-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-other-cloudmodel-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-water", "url": "https://cloudmodel-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-other-forceecrm", "name": "复制云(招商局)", "icon": "🌧️", "sites": [{"id": "foneshare-cloud-other-forceecrm-cms", "name": "配置中心", "description": "系统的集中化配置管理中心。", "icon": "⚙️", "iconClass": "fas fa-cogs", "url": "https://forceecrm-prod.foneshare.cn/cms/", "tags": ["cms", "config", "admin"]}, {"id": "foneshare-cloud-other-forceecrm-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://forceecrm-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-other-forceecrm-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://forceecrm-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-other-forceecrm-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-alt", "url": "https://forceecrm-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-other-forceecrm-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-other-forceecrm-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://forceecrm-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-other-forceecrm-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://forceecrm-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-other-forceecrm-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://forceecrm-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-other-forceecrm-cctrlCenter", "name": "云控", "description": "云端集中控制与管理中心。", "icon": "📡", "iconClass": "fas fa-satellite-dish", "url": "https://forceecrm-prod.foneshare.cn/cctrl-center/config/show", "tags": ["control", "center", "config"]}, {"id": "foneshare-cloud-other-forceecrm-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-water", "url": "https://forceecrm-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-other-ucd", "name": "性能测试云", "icon": "🌧️", "sites": [{"id": "foneshare-cloud-other-ucd-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://ucd-test.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-other-ucd-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://ucd-test.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-other-ucd-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-alt", "url": "https://ucd-test-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-other-ucd-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-other-ucd-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://ucd-test.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-other-ucd-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://ucd-test.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-other-ucd-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://ucd-test-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-other-ucd-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-water", "url": "https://ucdtest-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-other-allink8s", "name": "allink8s", "icon": "🌧️", "sites": [{"id": "foneshare-cloud-other-allink8s-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://allink8s-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-other-allink8s-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://allink8s-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-other-allink8s-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-alt", "url": "https://allink8s-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-other-allink8s-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-line", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-other-allink8s-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://allink8s-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-other-allink8s-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://allink8s-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-other-allink8s-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://allink8s-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-other-allink8s-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-water", "url": "https://allink8s-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}]}]}]}