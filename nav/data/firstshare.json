{"categories": [{"id": "sr", "name": "应用发布", "icon": "🌈", "iconClass": "fas fa-wand-magic-sparkles", "children": [{"id": "sr-app", "name": "服务发布", "icon": "⭐", "iconClass": "fas fa-star", "sites": [{"id": "old-nav", "name": "旧版导航页", "description": "当前导航页未找到跳转链接可切回旧版", "icon": "🚪", "iconClass": "fas fa-door-open", "url": "https://oss.firstshare.cn/navigation/", "tags": ["发布", "K8s", "部署", "运维"]}, {"id": "k8s-app", "name": "发布系统", "description": "Kubernetes应用发布管理系统", "icon": "☸️", "url": "https://k8s-app.firstshare.cn/", "markdownFile": "nav/data/docs/k8s-deploy-guide.md", "tags": ["发布", "K8s", "部署", "运维"]}, {"id": "vm-deploy", "name": "发布系统(虚机应用)", "description": "虚拟机应用发布系统", "icon": "🖥️", "iconClass": "fas fa-desktop", "url": "https://oss.firstshare.cn/publish/deploy/", "tags": ["发布", "虚机", "部署", "运维"]}, {"id": "jenkins", "name": "<PERSON>", "description": "持续集成构建平台", "icon": "🔨", "iconClass": "fab fa-jenkins", "url": "http://jenkins.firstshare.cn/", "tags": ["CI/CD", "构建", "自动化", "<PERSON>"]}, {"id": "miniprogram-release", "name": "小程序发布(fstest)", "description": "小程序发布管理平台", "icon": "📱", "iconClass": "fas fa-mobile-screen-button", "url": "https://112.firstshare.cn/weex-console/wx/list/", "tags": ["小程序", "发布", "管理", "fstest"]}]}, {"id": "sr-config", "name": "配置修改", "icon": "🔖", "iconClass": "fas fa-sliders", "sites": [{"id": "config-center-new", "name": "配置中心(新版)", "description": "新版配置管理中心", "icon": "⚙️", "iconClass": "fas fa-gear", "url": "https://console.firstshare.cn/cms/#/configuration/profile/list", "tags": ["配置", "管理", "新版", "运维"]}, {"id": "config-center-old", "name": "配置中心(旧版)", "description": "旧版配置管理中心", "icon": "🔧", "iconClass": "fas fa-wrench", "url": "https://oss.firstshare.cn/cms/", "tags": ["配置", "管理", "旧版", "运维"]}, {"id": "cloud-control-center", "name": "云控中心(fstest)", "description": "云控制中心管理平台", "icon": "☁️", "iconClass": "fas fa-cloud", "url": "https://oss.firstshare.cn/cctrl-center-fstest/", "tags": ["云控", "控制", "管理", "fstest"]}, {"id": "action-router", "name": "Action Router(fstest)", "description": "灰度路由管理系统", "icon": "🔀", "iconClass": "fas fa-route", "url": "https://oss.firstshare.cn/router-console-112/", "tags": ["路由", "灰度", "管理", "fstest"]}]}, {"id": "monitoring", "name": "日志监控", "icon": "📊", "iconClass": "fas fa-chart-bar", "sites": [{"id": "clickhouse-log-center", "name": "Clickhouse", "description": "基于Clickhouse的日志中心", "icon": "📊", "iconClass": "fas fa-database", "url": "https://log.firstshare.cn/", "tags": ["日志", "Clickhouse", "中心", "查询"]}, {"id": "grafana-log-center", "name": "Grafana日志导航", "description": "基于Grafana的日志中心", "icon": "📊", "iconClass": "fas fa-chart-line", "url": "https://grafana.firstshare.cn/d/e8bf99ce-2bae-4b8c-a528-c12dced8f407/e8a786-e59bbe-e5afbc-e888aa-e9a1b5?orgId=1&from=now-15m&to=now&timezone=browser", "tags": ["日志", "Clickhouse", "中心", "查询"]}, {"id": "app-log-monitor", "name": "app.log", "description": "Grafana应用日志看板", "icon": "📝", "iconClass": "fas fa-file-lines", "url": "https://grafana.firstshare.cn/d/ch-app-logs/clickhouse-app-logs", "tags": ["应用", "日志", "监控", "系统"]}, {"id": "log-level", "name": "日志级别调整", "description": "动态调整应用日志级别", "icon": "⚙️", "iconClass": "fas fa-sliders", "url": "https://oss.firstshare.cn/logconf/", "tags": ["日志", "级别", "调整", "配置"]}, {"id": "jstack-analysis", "name": "jstack分析", "description": "Java线程堆栈分析工具", "icon": "🔍", "iconClass": "fas fa-layer-group", "url": "https://oss.firstshare.cn/jstack-view/", "tags": ["Java", "线程", "分析", "调试"]}, {"id": "cep-monitor", "name": "CEP监控", "description": "CEP调用及错误监控", "icon": "📊", "iconClass": "fas fa-chart-pie", "url": "https://grafana.firstshare.cn/d/ch-log-cep/clickhouse-log-cep?var-interval=1m&orgId=1&from=now-3h&to=now&timezone=browser&var-datasource=vBBSZWs7z&var-cnt=10&var-biz=$__all&var-bizName=$__all&var-ea=$__all&var-filter=&var-condition=1&refresh=1m", "tags": ["CEP", "监控", "错误", "调用"]}, {"id": "load-monitor", "name": "负载监控", "description": "系统负载监控", "icon": "⚖️", "iconClass": "fas fa-scale-balanced", "url": "https://grafana.firstshare.cn/d/load-monitor/xi-tong-fu-zai-jian-kong?orgId=1", "tags": ["负载", "监控", "系统", "性能"]}, {"id": "process-monitor", "name": "进程监控", "description": "多机进程监控", "icon": "⚙️", "iconClass": "fas fa-microchip", "url": "https://grafana.firstshare.cn/d/process-monitor/duo-ji-jin-cheng-jian-kong?orgId=1", "tags": ["进程", "监控", "多机", "系统"]}, {"id": "single-node-monitor", "name": "单机监控", "description": "Node Exporter单机监控", "icon": "💻", "iconClass": "fas fa-server", "url": "https://grafana.firstshare.cn/d/single-node/node-exporter-full?orgId=1", "tags": ["单机", "监控", "Node", "Exporter"]}]}, {"id": "console", "name": "管理后台", "icon": "🔧", "iconClass": "fas fa-screwdriver-wrench", "sites": [{"id": "restful-admin", "name": "文件系统管理后台", "description": "RESTful API管理后台", "icon": "🔧", "iconClass": "fas fa-folder-open", "url": "https://oss.firstshare.cn/fs-stone-admin", "tags": ["RESTful", "API", "管理", "后台"]}, {"id": "bi-admin", "name": "BI后台管理系统", "description": "商业智能后台管理系统", "icon": "📊", "iconClass": "fas fa-chart-column", "url": "https://oss.firstshare.cn/bims/", "tags": ["BI", "后台", "管理", "系统"]}, {"id": "i18n-console", "name": "国际化管理平台", "description": "多语言国际化管理平台", "icon": "🌍", "iconClass": "fas fa-globe", "url": "http://oss.firstshare.cn/i18n-console", "tags": ["国际化", "多语言", "管理", "平台"]}]}, {"id": "documentation", "name": "文档规范", "icon": "📚", "iconClass": "fas fa-book", "sites": [{"id": "release-spec", "name": "研发中心发布变更规范3.0", "description": "研发中心发布变更规范文档", "icon": "📋", "iconClass": "fas fa-clipboard-list", "url": "https://wiki.firstshare.cn/pages/viewpage.action?pageId=103188954", "tags": ["发布", "规范", "变更", "研发"]}, {"id": "tech-spec", "name": "技术规范导航页", "description": "技术规范文档导航", "icon": "🧭", "iconClass": "fas fa-compass", "url": "https://wiki.firstshare.cn/pages/viewpage.action?pageId=176134427", "tags": ["技术", "规范", "导航", "文档"]}, {"id": "devops-intro", "name": "纷享DevOps平台介绍", "description": "纷享DevOps平台使用介绍", "icon": "🔧", "iconClass": "fas fa-circle-info", "url": "https://sharecrm.feishu.cn/wiki/wikcnR0NeujciC5EEtyOtnHMJdd", "tags": ["DevOps", "平台", "介绍", "纷享"]}, {"id": "log-platform", "name": "纷享日志平台-接入和查询", "description": "日志平台接入和查询指南", "icon": "📊", "iconClass": "fas fa-book-open-reader", "url": "https://sharecrm.feishu.cn/wiki/wikcnpERP1FppAMp4s83g4ur4oc", "tags": ["日志", "平台", "接入", "查询"]}]}]}, {"id": "operations", "name": "运维管理", "icon": "⛱️", "iconClass": "fas fa-shield-halved", "sites": [{"id": "ops-platform", "name": "OpsPlatform", "description": "运维管理平台", "icon": "🛠️", "iconClass": "fas fa-screwdriver-wrench", "url": "https://mon.foneshare.cn/", "tags": ["运维", "管理", "平台", "监控"]}, {"id": "cmdb", "name": "CMDB", "description": "配置管理数据库", "icon": "📊", "iconClass": "fas fa-database", "url": "https://cmdb.foneshare.cn/index.php/", "tags": ["配置", "数据库", "管理", "CMDB"]}, {"id": "mysql-tool", "name": "MySQL工具", "description": "MySQL数据库管理工具", "icon": "🗄️", "iconClass": "fas fa-database", "url": "http://archery.firstshare.cn/", "tags": ["MySQL", "数据库", "管理", "工具"]}, {"id": "kubepi", "name": "KubePi", "description": "Kubernetes集群管理工具", "icon": "☸️", "url": "http://oss.firstshare.cn/kubepi", "tags": ["Kubernetes", "集群", "管理", "工具"]}, {"id": "dubboAdmin", "name": "Dubbo-Admin", "description": "Dubbo服务管理控制台", "icon": "🔗", "iconClass": "fas fa-sitemap", "url": "http://************:33380/", "tags": ["Dubbo", "服务", "管理", "fstest"]}, {"id": "rocketmqConsole", "name": "RocketMQ-Console", "description": "RocketMQ控制台", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "http://oss.firstshare.cn/rmq-console-112/", "tags": ["RocketMQ", "消息队列", "监控", "MQ"]}, {"id": "kafka-manager", "name": "<PERSON><PERSON><PERSON>-Manager", "description": "Kafka消息队列管理工具(fsdevops)", "icon": "📨", "iconClass": "fas fa-envelopes-bulk", "url": "https://oss.firstshare.cn/kafka-manager", "tags": ["Kafka", "消息队列", "管理", "fsdevops"]}, {"id": "kafka-log-center", "name": "Kafka-RedPanda", "description": "基于RedPanda的Kafka日志中心", "icon": "🐼", "iconClass": "fas fa-paw", "url": "http://************:31190/", "tags": ["Kafka", "日志", "Red<PERSON>anda", "中心"]}, {"id": "cerebro", "name": "cerebro", "description": "Elasticsearch集群管理工具", "icon": "🍥", "iconClass": "fas fa-brain", "url": "https://oss.firstshare.cn/cerebro/", "tags": ["Elasticsearch", "集群", "管理", "工具"]}]}, {"id": "code-related", "name": "代码管理", "icon": "🌀", "iconClass": "fas fa-code", "sites": [{"id": "git-repo", "name": "GitLab", "description": "代码版本管理仓库", "icon": "🦊", "iconClass": "fab fa-gitlab", "url": "https://git.firstshare.cn/", "markdownFile": "nav/data/docs/git-guide.md", "tags": ["Git", "代码", "版本控制", "仓库"]}, {"id": "sourcegraph", "name": "SourceGraph", "description": "代码搜索和浏览工具", "icon": "🔍", "iconClass": "fas fa-magnifying-glass-location", "url": "https://sourcegraph.firstshare.cn/", "tags": ["代码搜索", "浏览", "SourceGraph", "Git"]}, {"id": "maven-repo", "name": "<PERSON><PERSON>", "description": "Maven依赖管理仓库", "icon": "📦", "iconClass": "fas fa-box-archive", "url": "https://maven.firstshare.cn/", "tags": ["<PERSON><PERSON>", "依赖", "仓库", "Artifactory"]}, {"id": "code-scan", "name": "SonarQube", "description": "代码质量扫描工具", "icon": "🔎", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://oss.firstshare.cn/sonarqube/", "tags": ["代码扫描", "质量", "SonarQube", "检查"]}, {"id": "scaffold", "name": "SpringBoot脚手架", "description": "项目脚手架生成工具", "icon": "🏗️", "iconClass": "fas fa-trowel-bricks", "url": "https://oss.firstshare.cn/starter/", "tags": ["脚手架", "项目", "生成", "模板"]}, {"id": "parent-pom", "name": "父POM版本", "description": "父POM组件版本对比工具", "icon": "📋", "iconClass": "fas fa-code-compare", "url": "https://git.firstshare.cn/JavaCommon/parent-pom/-/blob/master/version-diff.md", "tags": ["POM", "版本", "组件", "对比"]}]}]}