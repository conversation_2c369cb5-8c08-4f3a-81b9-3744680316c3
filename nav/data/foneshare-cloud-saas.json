{"categories": [{"id": "foneshare-cloud", "name": "多云管理", "icon": "🌐", "iconClass": "fas fa-globe", "children": [{"id": "foneshare-cloud-saas", "name": "SaaS", "icon": "☁️", "iconClass": "fas fa-cloud", "children": [{"id": "foneshare-cloud-saas-huaweicloud", "name": "华为云", "icon": "☁️", "iconClass": "fab fa-huawei", "sites": [{"id": "foneshare-cloud-saas-huaweicloud-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://hwc-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-huaweicloud-kibana", "name": "Kibana", "description": "Kibana日志查询与可视化分析平台。", "icon": "🔍", "iconClass": "fas fa-magnifying-glass-chart", "url": "https://hwc-prod.foneshare.cn/kibana4318", "tags": ["kibana", "log", "dashboard"]}, {"id": "foneshare-cloud-saas-huaweicloud-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-area", "url": "https://hwc-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-huaweicloud-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://oss.foneshare.cn/javaconsole-hw/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-huaweicloud-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://hwc-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-huaweicloud-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台，用于消息队列监控。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://hwc-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-huaweicloud-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-pointed", "url": "https://hw-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-huaweicloud-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://hwc-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-huaweicloud-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://hwc-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-ale", "name": "阿里云", "icon": "☁️", "iconClass": "fab fa-aliyun", "sites": [{"id": "foneshare-cloud-saas-ale-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://ale-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-ale-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://ale-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-ale-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-pointed", "url": "https://ale-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-ale-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-area", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-ale-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://ale-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-ale-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://ale-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-ale-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://ale-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-ale-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://ale-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-eu", "name": "亚马逊法兰克福", "icon": "☁️", "iconClass": "fab fa-aws", "sites": [{"id": "foneshare-cloud-saas-eu-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://hws-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-eu-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://hws-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-eu-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-pointed", "url": "https://hws-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-eu-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-area", "url": "https://hws-prod.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-eu-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://hws-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-eu-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://hws-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-eu-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://hws-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-eu-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://hws-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-hk", "name": "亚马逊中国香港", "icon": "☁️", "iconClass": "fab fa-aws", "sites": [{"id": "foneshare-cloud-saas-hk-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://ksc-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-hk-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://ksc-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-hk-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-pointed", "url": "https://ksc-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-hk-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-area", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-hk-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://ksc-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-hk-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://ksc-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-hk-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://ksc-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-hk-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://ksc-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-sea", "name": "亚马逊东南亚新加坡", "icon": "☁️", "iconClass": "fab fa-aws", "sites": [{"id": "foneshare-cloud-saas-sea-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://forsharecrm-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-sea-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://forsharecrm-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-sea-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-pointed", "url": "https://forsharecrm-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-sea-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-area", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-sea-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://forsharecrm-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-sea-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://forsharecrm-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-sea-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://forsharecrm-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-sea-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://forsharecrm-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-na", "name": "亚马逊北美北加州", "icon": "☁️", "iconClass": "fab fa-aws", "sites": [{"id": "foneshare-cloud-saas-na-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "iconClass": "fab fa-java", "url": "https://kemaicrm-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-na-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "iconClass": "fas fa-rocket", "url": "https://kemaicrm-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-na-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "iconClass": "fas fa-feather-pointed", "url": "https://kemaicrm-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-na-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "iconClass": "fas fa-chart-area", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-na-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "iconClass": "fas fa-clock", "url": "https://kemaicrm-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-na-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "iconClass": "fas fa-layer-group", "url": "https://kemaicrm-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-na-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "iconClass": "fas fa-bolt", "url": "https://kemaicrm-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-na-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "iconClass": "fas fa-right-left", "url": "https://kemaicrm-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}]}]}]}