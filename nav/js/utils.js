// 工具函数集合

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间(毫秒)
 * @param {boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait, immediate = false) {
    let timeout;

    function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(this, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(this, args);
    }

    // 添加取消方法
    executedFunction.cancel = function() {
        clearTimeout(timeout);
        timeout = null;
    };

    return executedFunction;
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 限制时间(毫秒)
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 深拷贝对象
 * @param {*} obj 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

/**
 * 扁平化嵌套的分类数据
 * @param {Array} categories 分类数组
 * @param {string} parentId 父级ID
 * @returns {Array} 扁平化后的数组
 */
function flattenCategories(categories, parentId = null) {
    let result = [];
    
    categories.forEach(category => {
        const flatCategory = {
            ...category,
            parentId,
            sites: category.sites || []
        };
        
        result.push(flatCategory);
        
        if (category.children && category.children.length > 0) {
            result = result.concat(flattenCategories(category.children, category.id));
        }
    });
    
    return result;
}

/**
 * 从扁平化数组中获取所有网站
 * @param {Array} categories 分类数组
 * @returns {Array} 所有网站的数组
 */
function getAllSites(categories) {
    const allSites = [];
    const seenSiteIds = new Set(); // 用于去重，防止多数据源导致的重复

    function extractSites(cats) {
        cats.forEach(category => {
            if (category.sites && category.sites.length > 0) {
                category.sites.forEach(site => {
                    // 检查是否已经添加过这个网站（基于ID去重）
                    if (!seenSiteIds.has(site.id)) {
                        allSites.push({
                            ...site,
                            categoryId: category.id,
                            categoryName: category.name
                        });
                        seenSiteIds.add(site.id);
                    } else {
                        console.log(`跳过重复网站: ${site.name} (ID: ${site.id})`);
                    }
                });
            }

            if (category.children && category.children.length > 0) {
                extractSites(category.children);
            }
        });
    }

    extractSites(categories);
    console.log(`提取网站完成: ${allSites.length} 个唯一网站`);
    return allSites;
}

/**
 * 高亮搜索关键词
 * @param {string} text 原文本
 * @param {string} keyword 关键词
 * @returns {string} 高亮后的HTML
 */
function highlightKeyword(text, keyword) {
    if (!keyword || !text) return text;
    
    const regex = new RegExp(`(${keyword})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

/**
 * 简单的模糊搜索算法
 * @param {string} query 搜索查询
 * @param {string} target 目标字符串
 * @returns {number} 匹配度分数 (0-1)
 */
function fuzzySearch(query, target) {
    if (!query || !target) return 0;
    
    query = query.toLowerCase();
    target = target.toLowerCase();
    
    // 精确匹配
    if (target.includes(query)) {
        return target === query ? 1 : 0.8;
    }
    
    // 模糊匹配
    let score = 0;
    let queryIndex = 0;
    
    for (let i = 0; i < target.length && queryIndex < query.length; i++) {
        if (target[i] === query[queryIndex]) {
            score += 1;
            queryIndex++;
        }
    }
    
    return score / query.length * 0.6;
}

/**
 * 格式化URL，确保包含协议
 * @param {string} url 原始URL
 * @returns {string} 格式化后的URL
 */
function formatUrl(url) {
    if (!url) return '';
    
    // 如果没有协议，默认添加https://
    if (!/^https?:\/\//i.test(url)) {
        return `https://${url}`;
    }
    
    return url;
}

/**
 * 提取域名
 * @param {string} url URL字符串
 * @returns {string} 域名
 */
function extractDomain(url) {
    try {
        const formattedUrl = formatUrl(url);
        const urlObj = new URL(formattedUrl);
        return urlObj.hostname.replace('www.', '');
    } catch (error) {
        return url;
    }
}

/**
 * 显示Toast消息
 * @param {string} message 消息内容
 * @param {string} type 消息类型 (success, error, warning, info)
 * @param {number} duration 显示时长(毫秒)
 */
function showToast(message, type = 'info', duration = 3000) {
    // 创建Toast元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // 添加到页面
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => toast.classList.add('show'), 100);
    
    // 自动移除
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
}

/**
 * 获取Toast图标
 * @param {string} type Toast类型
 * @returns {string} 图标名称
 */
function getToastIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * 生成随机ID
 * @param {number} length ID长度
 * @returns {string} 随机ID
 */
function generateId(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * 检查是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
function isMobile() {
    return window.innerWidth <= 767;
}

/**
 * 检查是否为触摸设备
 * @returns {boolean} 是否为触摸设备
 */
function isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

/**
 * 平滑滚动到指定元素
 * @param {string|Element} target 目标元素或选择器
 * @param {number} offset 偏移量
 */
function scrollToElement(target, offset = 0) {
    const element = typeof target === 'string' ? document.querySelector(target) : target;
    if (!element) return;
    
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;
    
    window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
    });
}

/**
 * 检查字符串是否为图片URL
 * @param {string} str 要检查的字符串
 * @returns {boolean} 是否为图片URL
 */
function isImageUrl(str) {
    if (!str || typeof str !== 'string') return false;
    
    // 检查是否为HTTP/HTTPS URL或相对路径
    const isUrl = /^https?:\/\//.test(str) || str.startsWith('/') || str.startsWith('./') || str.startsWith('../') || str.includes('assets/');
    
    // 检查文件扩展名
    const imageExtensions = /\.(png|jpg|jpeg|gif|svg|webp|ico|bmp)(\?.*)?$/i;
    
    return isUrl && imageExtensions.test(str);
}

/**
 * 获取图标类型
 * @param {string} icon 图标字符串
 * @returns {string} 图标类型：'image' | 'unicode'
 */
function getIconType(icon) {
    if (!icon) return 'unicode';
    return isImageUrl(icon) ? 'image' : 'unicode';
}

/**
 * 渲染图标HTML
 * @param {string} icon 图标字符串
 * @param {string} alt 图片的alt属性
 * @param {string} iconClass Font Awesome图标类名
 * @returns {string} 图标HTML
 */
function renderIcon(icon, alt = '图标', iconClass = '') {
    // 优先使用 iconClass（Font Awesome 图标）
    if (iconClass && iconClass.trim() !== '') {
        return `<i class="${iconClass}" aria-label="${alt}"></i>`;
    }

    if (!icon) {
        return '🌐'; // 默认图标
    }

    const iconType = getIconType(icon);

    if (iconType === 'image') {
        return `<img src="${icon}" alt="${alt}" class="site-icon-image" loading="lazy" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <span class="site-icon-fallback" style="display:none;">🌐</span>`;
    } else {
        return icon;
    }
}

/**
 * 中文拼音首字母映射表（常用汉字）
 * 基于GB2312字符集的常用汉字拼音首字母
 */
const PINYIN_INITIAL_MAP = {
    // A
    '阿': 'A', '啊': 'A', '爱': 'A', '安': 'A', '按': 'A', '案': 'A', '暗': 'A',
    // B
    '把': 'B', '吧': 'B', '白': 'B', '百': 'B', '办': 'B', '帮': 'B', '包': 'B', '报': 'B', '被': 'B', '本': 'B', '比': 'B', '别': 'B', '并': 'B', '不': 'B', '部': 'B', '布': 'B',
    // C
    '才': 'C', '菜': 'C', '参': 'C', '草': 'C', '测': 'C', '查': 'C', '产': 'C', '常': 'C', '车': 'C', '成': 'C', '程': 'C', '吃': 'C', '出': 'C', '处': 'C', '创': 'C', '从': 'C', '存': 'C',
    // D
    '大': 'D', '带': 'D', '单': 'D', '当': 'D', '到': 'D', '得': 'D', '的': 'D', '地': 'D', '点': 'D', '电': 'D', '定': 'D', '东': 'D', '动': 'D', '都': 'D', '对': 'D', '多': 'D',
    // E
    '而': 'E', '儿': 'E', '二': 'E',
    // F
    '发': 'F', '法': 'F', '反': 'F', '方': 'F', '放': 'F', '非': 'F', '分': 'F', '风': 'F', '服': 'F', '福': 'F', '复': 'F',
    // G
    '该': 'G', '感': 'G', '干': 'G', '刚': 'G', '高': 'G', '个': 'G', '给': 'G', '根': 'G', '更': 'G', '工': 'G', '公': 'G', '功': 'G', '管': 'G', '关': 'G', '广': 'G', '国': 'G', '过': 'G',
    // H
    '还': 'H', '海': 'H', '好': 'H', '号': 'H', '和': 'H', '很': 'H', '后': 'H', '户': 'H', '化': 'H', '话': 'H', '环': 'H', '回': 'H', '会': 'H', '活': 'H', '火': 'H',
    // J
    '机': 'J', '基': 'J', '及': 'J', '级': 'J', '即': 'J', '技': 'J', '计': 'J', '记': 'J', '加': 'J', '家': 'J', '价': 'J', '间': 'J', '建': 'J', '见': 'J', '将': 'J', '教': 'J', '接': 'J', '结': 'J', '解': 'J', '进': 'J', '经': 'J', '就': 'J', '据': 'J',
    // K
    '开': 'K', '看': 'K', '可': 'K', '客': 'K', '空': 'K', '快': 'K',
    // L
    '来': 'L', '老': 'L', '了': 'L', '类': 'L', '里': 'L', '理': 'L', '力': 'L', '立': 'L', '连': 'L', '两': 'L', '量': 'L', '列': 'L', '流': 'L', '路': 'L', '论': 'L',
    // M
    '吗': 'M', '马': 'M', '买': 'M', '卖': 'M', '满': 'M', '没': 'M', '每': 'M', '美': 'M', '们': 'M', '面': 'M', '民': 'M', '名': 'M', '明': 'M', '目': 'M',
    // N
    '那': 'N', '哪': 'N', '内': 'N', '能': 'N', '你': 'N', '年': 'N', '女': 'N',
    // P
    '排': 'P', '跑': 'P', '配': 'P', '平': 'P', '品': 'P', '评': 'P',
    // Q
    '其': 'Q', '起': 'Q', '前': 'Q', '钱': 'Q', '强': 'Q', '请': 'Q', '情': 'Q', '区': 'Q', '去': 'Q', '全': 'Q', '确': 'Q',
    // R
    '然': 'R', '让': 'R', '人': 'R', '认': 'R', '任': 'R', '如': 'R', '入': 'R',
    // S
    '三': 'S', '色': 'S', '设': 'S', '社': 'S', '生': 'S', '时': 'S', '十': 'S', '实': 'S', '使': 'S', '是': 'S', '事': 'S', '手': 'S', '首': 'S', '受': 'S', '数': 'S', '说': 'S', '思': 'S', '所': 'S', '搜': 'S',
    // T
    '他': 'T', '她': 'T', '它': 'T', '台': 'T', '太': 'T', '特': 'T', '提': 'T', '题': 'T', '体': 'T', '天': 'T', '条': 'T', '通': 'T', '同': 'T', '头': 'T', '图': 'T', '推': 'T',
    // W
    '外': 'W', '完': 'W', '网': 'W', '为': 'W', '位': 'W', '文': 'W', '问': 'W', '我': 'W', '无': 'W', '五': 'W',
    // X
    '下': 'X', '先': 'X', '现': 'X', '线': 'X', '相': 'X', '想': 'X', '向': 'X', '小': 'X', '新': 'X', '信': 'X', '行': 'X', '形': 'X', '学': 'X', '选': 'X', '需': 'X',
    // Y
    '要': 'Y', '也': 'Y', '业': 'Y', '一': 'Y', '以': 'Y', '已': 'Y', '用': 'Y', '有': 'Y', '又': 'Y', '于': 'Y', '与': 'Y', '语': 'Y', '元': 'Y', '原': 'Y', '员': 'Y', '月': 'Y', '运': 'Y',
    // Z
    '在': 'Z', '再': 'Z', '早': 'Z', '怎': 'Z', '增': 'Z', '展': 'Z', '站': 'Z', '这': 'Z', '正': 'Z', '之': 'Z', '知': 'Z', '直': 'Z', '只': 'Z', '中': 'Z', '种': 'Z', '重': 'Z', '主': 'Z', '注': 'Z', '专': 'Z', '状': 'Z', '准': 'Z', '资': 'Z', '自': 'Z', '总': 'Z', '组': 'Z', '作': 'Z', '做': 'Z'
};

/**
 * 获取中文字符的拼音首字母
 * @param {string} char 单个中文字符
 * @returns {string} 拼音首字母，如果不是中文字符则返回原字符的大写
 */
function getChinesePinyinInitial(char) {
    // 如果在映射表中找到，直接返回
    if (PINYIN_INITIAL_MAP[char]) {
        return PINYIN_INITIAL_MAP[char];
    }

    // 使用Unicode编码范围判断并转换（简化版）
    const code = char.charCodeAt(0);

    // 基本汉字Unicode范围：4E00-9FFF
    if (code >= 0x4E00 && code <= 0x9FFF) {
        // 简化的拼音首字母推算（基于Unicode编码区间）
        if (code >= 0x4E00 && code <= 0x4FFF) return 'A';
        if (code >= 0x5000 && code <= 0x51FF) return 'B';
        if (code >= 0x5200 && code <= 0x53FF) return 'C';
        if (code >= 0x5400 && code <= 0x55FF) return 'D';
        if (code >= 0x5600 && code <= 0x56FF) return 'E';
        if (code >= 0x5700 && code <= 0x58FF) return 'F';
        if (code >= 0x5900 && code <= 0x5AFF) return 'G';
        if (code >= 0x5B00 && code <= 0x5CFF) return 'H';
        if (code >= 0x5D00 && code <= 0x5EFF) return 'J';
        if (code >= 0x5F00 && code <= 0x60FF) return 'K';
        if (code >= 0x6100 && code <= 0x62FF) return 'L';
        if (code >= 0x6300 && code <= 0x64FF) return 'M';
        if (code >= 0x6500 && code <= 0x66FF) return 'N';
        if (code >= 0x6700 && code <= 0x68FF) return 'P';
        if (code >= 0x6900 && code <= 0x6AFF) return 'Q';
        if (code >= 0x6B00 && code <= 0x6CFF) return 'R';
        if (code >= 0x6D00 && code <= 0x6EFF) return 'S';
        if (code >= 0x6F00 && code <= 0x70FF) return 'T';
        if (code >= 0x7100 && code <= 0x72FF) return 'W';
        if (code >= 0x7300 && code <= 0x74FF) return 'X';
        if (code >= 0x7500 && code <= 0x76FF) return 'Y';
        if (code >= 0x7700 && code <= 0x9FFF) return 'Z';
    }

    // 如果不是中文字符，返回原字符的大写
    return char.toUpperCase();
}

/**
 * 获取字符串的首字母组合
 * @param {string} text 输入文本
 * @returns {string} 首字母组合
 */
function getInitials(text) {
    if (!text || typeof text !== 'string') return '';

    return text
        .split('')
        .map(char => {
            // 英文字母直接返回大写
            if (/[a-zA-Z]/.test(char)) {
                return char.toUpperCase();
            }
            // 数字直接返回
            if (/[0-9]/.test(char)) {
                return char;
            }
            // 中文字符转拼音首字母
            if (/[\u4e00-\u9fff]/.test(char)) {
                return getChinesePinyinInitial(char);
            }
            // 其他字符忽略
            return '';
        })
        .join('');
}

/**
 * 检查查询字符串是否匹配目标文本的首字母
 * @param {string} query 查询字符串
 * @param {string} target 目标文本
 * @returns {object} 匹配结果 {isMatch: boolean, score: number}
 */
function matchInitials(query, target) {
    if (!query || !target) {
        return { isMatch: false, score: 0 };
    }

    const queryUpper = query.toUpperCase();
    const targetInitials = getInitials(target);

    // 完全匹配首字母
    if (targetInitials === queryUpper) {
        return { isMatch: true, score: 1.0 };
    }

    // 前缀匹配首字母
    if (targetInitials.startsWith(queryUpper)) {
        return { isMatch: true, score: 0.8 };
    }

    // 包含匹配首字母
    if (targetInitials.includes(queryUpper)) {
        return { isMatch: true, score: 0.6 };
    }

    // 模糊匹配首字母（允许部分字符不匹配）
    let matchCount = 0;
    let queryIndex = 0;

    for (let i = 0; i < targetInitials.length && queryIndex < queryUpper.length; i++) {
        if (targetInitials[i] === queryUpper[queryIndex]) {
            matchCount++;
            queryIndex++;
        }
    }

    if (matchCount > 0 && matchCount >= queryUpper.length * 0.6) {
        const fuzzyScore = (matchCount / queryUpper.length) * 0.4;
        return { isMatch: true, score: fuzzyScore };
    }

    return { isMatch: false, score: 0 };
}

// 导出工具函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        debounce,
        throttle,
        deepClone,
        flattenCategories,
        getAllSites,
        highlightKeyword,
        fuzzySearch,
        formatUrl,
        extractDomain,
        showToast,
        generateId,
        isMobile,
        isTouchDevice,
        scrollToElement,
        isImageUrl,
        getIconType,
        renderIcon,
        getChinesePinyinInitial,
        getInitials,
        matchInitials
    };
}